# Generated by Django 5.2.1 on 2025-05-30 11:05

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Category',
            fields=[
                ('CId', models.AutoField(db_column='CId', primary_key=True, serialize=False)),
                ('CName', models.CharField(db_column='CName', max_length=255, verbose_name='Category Name')),
                ('Symbol', models.CharField(db_column='Symbol', max_length=1, verbose_name='Symbol')),
                ('HasSubCat', models.CharField(db_column='HasSubCat', default='0', max_length=1, verbose_name='Has SubCategory')),
                ('SysDate', models.DateField(auto_now_add=True, db_column='SysDate', verbose_name='System Date')),
                ('SysTime', models.TimeField(auto_now_add=True, db_column='SysTime', verbose_name='System Time')),
                ('CompId', models.CharField(db_column='CompId', max_length=50, verbose_name='Company ID')),
                ('FinYearId', models.CharField(db_column='FinYearId', max_length=50, verbose_name='Financial Year ID')),
                ('SessionId', models.CharField(db_column='SessionId', max_length=100, verbose_name='Session ID')),
            ],
            options={
                'verbose_name': 'Category',
                'verbose_name_plural': 'Categories',
                'db_table': 'tblSD_WO_Category',
                'ordering': ['CName'],
                'managed': True,
                'unique_together': {('Symbol', 'CompId', 'FinYearId')},
            },
        ),
        migrations.CreateModel(
            name='SubCategory',
            fields=[
                ('SCId', models.AutoField(db_column='SCId', primary_key=True, serialize=False)),
                ('CId', models.IntegerField(db_column='CId', verbose_name='Category ID')),
                ('SCName', models.CharField(db_column='SCName', max_length=255, verbose_name='Sub-Category Name')),
                ('SCSymbol', models.CharField(db_column='SCSymbol', max_length=1, verbose_name='Sub-Category Symbol')),
                ('SysDate', models.DateField(auto_now_add=True, db_column='SysDate', verbose_name='System Date')),
                ('SysTime', models.TimeField(auto_now_add=True, db_column='SysTime', verbose_name='System Time')),
                ('CompId', models.CharField(db_column='CompId', max_length=50, verbose_name='Company ID')),
                ('FinYearId', models.CharField(db_column='FinYearId', max_length=50, verbose_name='Financial Year ID')),
                ('SessionId', models.CharField(db_column='SessionId', max_length=100, verbose_name='Session ID')),
            ],
            options={
                'verbose_name': 'Sub-Category',
                'verbose_name_plural': 'Sub-Categories',
                'db_table': 'tblSD_WO_SubCategory',
                'ordering': ['SCName'],
                'managed': True,
                'unique_together': {('SCSymbol', 'CId', 'CompId', 'FinYearId')},
            },
        ),
    ]
