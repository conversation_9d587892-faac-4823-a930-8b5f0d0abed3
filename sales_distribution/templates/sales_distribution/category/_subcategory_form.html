<div class="bg-white p-6 rounded-lg shadow-lg">
    <div class="flex justify-between items-center mb-5">
        <h3 class="text-2xl font-semibold text-gray-900">{{ form.instance.pk|yesno:'Edit,Add' }} Work Order Sub-Category</h3>
        <button type="button" class="text-gray-400 hover:text-gray-600 transition-colors duration-200"
                _="on click remove .is-active from #modal">
            <i class="fas fa-times text-xl"></i>
        </button>
    </div>
    
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#form-spinner">
        {% csrf_token %}
        
        <div class="space-y-5">
            <div class="relative">
                <label for="{{ form.CId.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ form.CId.label }}
                    <span class="text-red-500">*</span>
                </label>
                <select name="{{ form.CId.name }}" id="{{ form.CId.id_for_label }}" 
                        class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    <option value="">Select a category</option>
                    {% for category in categories %}
                    <option value="{{ category.CId }}" {% if form.instance.CId == category.CId %}selected{% endif %}>
                        {{ category.CName }} ({{ category.Symbol }})
                    </option>
                    {% endfor %}
                </select>
                {% if form.CId.help_text %}
                <p class="mt-1 text-xs text-gray-500">{{ form.CId.help_text }}</p>
                {% endif %}
                {% if form.CId.errors %}
                <ul class="text-red-600 text-sm mt-1 list-disc pl-5">
                    {% for error in form.CId.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>

            <div class="relative">
                <label for="{{ form.SCName.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ form.SCName.label }}
                    <span class="text-red-500">*</span>
                </label>
                {{ form.SCName }}
                {% if form.SCName.help_text %}
                <p class="mt-1 text-xs text-gray-500">{{ form.SCName.help_text }}</p>
                {% endif %}
                {% if form.SCName.errors %}
                <ul class="text-red-600 text-sm mt-1 list-disc pl-5">
                    {% for error in form.SCName.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>

            <div class="relative">
                <label for="{{ form.SCSymbol.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ form.SCSymbol.label }}
                    <span class="text-red-500">*</span>
                </label>
                {{ form.SCSymbol }}
                {% if form.SCSymbol.help_text %}
                <p class="mt-1 text-xs text-gray-500">{{ form.SCSymbol.help_text }}</p>
                {% endif %}
                {% if form.SCSymbol.errors %}
                <ul class="text-red-600 text-sm mt-1 list-disc pl-5">
                    {% for error in form.SCSymbol.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-semibold py-2 px-5 rounded-lg shadow-md transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-5 rounded-lg shadow-md transition duration-300 ease-in-out">
                <i class="fas fa-save mr-2"></i> Save Changes
            </button>
            <div id="form-spinner" class="htmx-indicator ml-3">
                <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
            </div>
        </div>
    </form>
</div>
