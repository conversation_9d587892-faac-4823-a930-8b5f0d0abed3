{% extends 'core/base.html' %}

{% block title %}Categories{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-3xl font-bold text-gray-900">Work Order Categories</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'category_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            <i class="fas fa-plus mr-2"></i> Add New Category
        </button>
    </div>
    
    <div id="categoryTable-container"
         hx-trigger="load, refreshCategoryList from:body"
         hx-get="{% url 'category_table' %}"
         hx-swap="innerHTML"
         class="bg-white shadow-xl rounded-lg p-6">
        <!-- Initial loading state -->
        <div class="flex flex-col items-center justify-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-4 border-blue-500"></div>
            <p class="mt-4 text-gray-600 text-lg">Loading categories...</p>
        </div>
    </div>
    
    <!-- Modal for form/confirmation -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center p-4 hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me"
         x-data="{ show: false }"
         x-show="show" x-transition:enter="ease-out duration-300"
         x-transition:enter-start="opacity-0 scale-90" x-transition:enter-end="opacity-100 scale-100"
         x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100 scale-100"
         x-transition:leave-end="opacity-0 scale-90">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-2xl max-w-2xl w-full mx-auto"
             _="on htmx:afterSwap remove .is-active from #modal end">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js is automatically initialized if included in base.html
    // DataTables initialization handled within the _category_table.html partial.
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        // Close modal after successful form submission (status 204 no content)
        if (evt.detail.xhr.status === 204) {
            document.getElementById('modal').classList.remove('is-active');
            // Hide any open modal if it's managed by Alpine.js
            let modalElement = document.getElementById('modal');
            if (modalElement && modalElement.__alpine && modalElement.__alpine.data) {
                modalElement.__alpine.data.show = false;
            }
        }
    });

    document.body.addEventListener('showToastMessage', function(evt) {
        // Placeholder for toast message display logic
        console.log("Show toast message event triggered!");
    });

</script>
{% endblock %}
