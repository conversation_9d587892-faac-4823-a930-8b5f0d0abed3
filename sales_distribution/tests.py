from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.messages import get_messages
from django.core.exceptions import ValidationError
from .models import Category, SubCategory
from .forms import CategoryForm, SubCategoryForm
import datetime


class CategoryModelTest(TestCase):
    """
    Unit tests for the Category model.
    """

    @classmethod
    def setUpTestData(cls):
        # Create a base category for common test scenarios
        cls.category_data = {
            "CName": "Test Category",
            "Symbol": "T",
            "HasSubCat": "1",
            "CompId": "COMP001",
            "FinYearId": "FY2023",
            "SessionId": "testuser",
        }
        cls.category = Category.objects.create(**cls.category_data)

    def test_category_creation(self):
        """Ensures a Category object can be created and its attributes are correct."""
        obj = Category.objects.get(CId=self.category.CId)
        self.assertEqual(obj.CName, "Test Category")
        self.assertEqual(obj.Symbol, "T")
        self.assertEqual(obj.HasSubCat, "1")
        self.assertEqual(obj.CompId, "COMP001")
        self.assertEqual(obj.FinYearId, "FY2023")
        self.assertEqual(obj.SessionId, "testuser")

    def test_str_method(self):
        """Tests the __str__ method for correct string representation."""
        obj = Category.objects.get(CId=self.category.CId)
        self.assertEqual(str(obj), "Test Category (T)")

    def test_has_subcategory_display_property(self):
        """Tests the `has_subcategory_display` property for correct string conversion."""
        cat_yes = Category.objects.create(
            CName="Cat With Sub",
            Symbol="Y",
            HasSubCat="1",
            CompId="COMP001",
            FinYearId="FY2024",
            SessionId="user1",
        )
        cat_no = Category.objects.create(
            CName="Cat No Sub",
            Symbol="N",
            HasSubCat="0",
            CompId="COMP001",
            FinYearId="FY2024",
            SessionId="user2",
        )
        self.assertEqual(cat_yes.has_subcategory_display, "Yes")
        self.assertEqual(cat_no.has_subcategory_display, "No")

    def test_symbol_uppercase_on_save(self):
        """Ensures the Symbol is converted to uppercase when saving."""
        new_cat = Category.objects.create(
            CName="Lowercase Symbol",
            Symbol="l",
            HasSubCat="0",
            CompId="COMP002",
            FinYearId="FY2023",
            SessionId="testuser",
        )
        self.assertEqual(new_cat.Symbol, "L")

    def test_has_sub_cat_boolean_to_string_conversion(self):
        """Tests that boolean values for HasSubCat are correctly converted to '1'/'0'."""
        cat_bool_true = Category.objects.create(
            CName="Bool True",
            Symbol="B",
            HasSubCat=True,
            CompId="COMP003",
            FinYearId="FY2023",
            SessionId="user",
        )
        cat_bool_false = Category.objects.create(
            CName="Bool False",
            Symbol="F",
            HasSubCat=False,
            CompId="COMP003",
            FinYearId="FY2023",
            SessionId="user",
        )
        self.assertEqual(cat_bool_true.HasSubCat, "1")
        self.assertEqual(cat_bool_false.HasSubCat, "0")

    def test_get_filtered_categories(self):
        """Tests the `get_filtered_categories` class method for correct filtering and ordering."""
        # Add more categories for filtering tests
        Category.objects.create(
            CName="Category B",
            Symbol="B",
            HasSubCat="0",
            CompId="COMP001",
            FinYearId="FY2022",
            SessionId="user",
        )
        Category.objects.create(
            CName="Category C",
            Symbol="C",
            HasSubCat="1",
            CompId="COMP002",
            FinYearId="FY2023",
            SessionId="user",
        )

        # Test filtering for COMP001 and FY2023 (should include FY2022 because of <=)
        filtered_cats = Category.get_filtered_categories("COMP001", "FY2023")
        self.assertEqual(
            filtered_cats.count(), 2
        )  # Test Category (FY2023) and Category B (FY2022)
        # Check order (desc by CId, so higher CId first) - Category B was created after Test Category
        self.assertEqual(
            filtered_cats.first().CName, "Category B"
        )


class SubCategoryModelTest(TestCase):
    """
    Unit tests for the SubCategory model.
    """

    @classmethod
    def setUpTestData(cls):
        # Create a category first
        cls.category = Category.objects.create(
            CName="Parent Category",
            Symbol="P",
            HasSubCat="1",
            CompId="COMP001",
            FinYearId="FY2023",
            SessionId="testuser",
        )

        # Create a subcategory
        cls.subcategory = SubCategory.objects.create(
            CId=cls.category.CId,
            SCName="Test SubCategory",
            SCSymbol="S",
            CompId="COMP001",
            FinYearId="FY2023",
            SessionId="testuser",
        )

    def test_subcategory_creation(self):
        """Ensures a SubCategory object can be created and its attributes are correct."""
        obj = SubCategory.objects.get(SCId=self.subcategory.SCId)
        self.assertEqual(obj.SCName, "Test SubCategory")
        self.assertEqual(obj.SCSymbol, "S")
        self.assertEqual(obj.CId, self.category.CId)
        self.assertEqual(obj.CompId, "COMP001")

    def test_str_method(self):
        """Tests the __str__ method for correct string representation."""
        obj = SubCategory.objects.get(SCId=self.subcategory.SCId)
        self.assertEqual(str(obj), "Test SubCategory (S)")

    def test_symbol_uppercase_on_save(self):
        """Ensures the SCSymbol is converted to uppercase when saving."""
        new_subcat = SubCategory.objects.create(
            CId=self.category.CId,
            SCName="Lowercase Symbol",
            SCSymbol="l",
            CompId="COMP001",
            FinYearId="FY2023",
            SessionId="testuser",
        )
        self.assertEqual(new_subcat.SCSymbol, "L")

    def test_get_category_method(self):
        """Tests the get_category method returns the correct category."""
        category = self.subcategory.get_category()
        self.assertEqual(category.CId, self.category.CId)
        self.assertEqual(category.CName, "Parent Category")

    def test_get_filtered_subcategories(self):
        """Tests the `get_filtered_subcategories` class method."""
        # Create another subcategory
        SubCategory.objects.create(
            CId=self.category.CId,
            SCName="Another SubCategory",
            SCSymbol="A",
            CompId="COMP001",
            FinYearId="FY2023",
            SessionId="testuser",
        )

        # Test filtering
        filtered_subcats = SubCategory.get_filtered_subcategories(
            "COMP001", "FY2023", self.category.CId
        )
        self.assertEqual(filtered_subcats.count(), 2)

        # Test filtering without category_id
        all_subcats = SubCategory.get_filtered_subcategories("COMP001", "FY2023")
        self.assertEqual(all_subcats.count(), 2)


class CategoryFormTest(TestCase):
    """
    Unit tests for the CategoryForm.
    """

    def test_category_form_valid(self):
        """Tests that the form is valid with correct data."""
        form_data = {
            "CName": "New Test Category",
            "Symbol": "N",
            "has_sub_cat_checkbox": True,
        }
        form = CategoryForm(data=form_data)
        self.assertTrue(form.is_valid(), msg=form.errors.as_text())

        # Verify has_sub_cat_checkbox maps correctly to instance.HasSubCat
        instance = form.save(commit=False)
        self.assertEqual(instance.HasSubCat, "1")

    def test_category_form_invalid_empty_fields(self):
        """Tests form validation for required fields."""
        form_data = {
            "CName": "",  # Missing
            "Symbol": "",  # Missing
            "has_sub_cat_checkbox": False,
        }
        form = CategoryForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn("CName", form.errors)
        self.assertIn("Symbol", form.errors)

    def test_category_form_symbol_uppercase_conversion(self):
        """Tests that the form's clean_Symbol converts input to uppercase."""
        form_data = {
            "CName": "Valid Name",
            "Symbol": "a",  # Lowercase input
            "has_sub_cat_checkbox": False,
        }
        form = CategoryForm(data=form_data)
        self.assertTrue(form.is_valid())
        self.assertEqual(form.cleaned_data["Symbol"], "A")  # Should be uppercase


class SubCategoryFormTest(TestCase):
    """
    Unit tests for the SubCategoryForm.
    """

    @classmethod
    def setUpTestData(cls):
        cls.category = Category.objects.create(
            CName="Parent Category",
            Symbol="P",
            HasSubCat="1",
            CompId="COMP001",
            FinYearId="FY2023",
            SessionId="testuser",
        )

    def test_subcategory_form_valid(self):
        """Tests that the form is valid with correct data."""
        form_data = {
            "CId": self.category.CId,
            "SCName": "New Test SubCategory",
            "SCSymbol": "N",
        }
        form = SubCategoryForm(data=form_data)
        self.assertTrue(form.is_valid(), msg=form.errors.as_text())

    def test_subcategory_form_invalid_empty_fields(self):
        """Tests form validation for required fields."""
        form_data = {
            "CId": "",  # Missing
            "SCName": "",  # Missing
            "SCSymbol": "",  # Missing
        }
        form = SubCategoryForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn("CId", form.errors)
        self.assertIn("SCName", form.errors)
        self.assertIn("SCSymbol", form.errors)

    def test_subcategory_form_symbol_uppercase_conversion(self):
        """Tests that the form's clean_SCSymbol converts input to uppercase."""
        form_data = {
            "CId": self.category.CId,
            "SCName": "Valid Name",
            "SCSymbol": "a",  # Lowercase input
        }
        form = SubCategoryForm(data=form_data)
        self.assertTrue(form.is_valid())
        self.assertEqual(form.cleaned_data["SCSymbol"], "A")  # Should be uppercase


class CategoryViewTest(TestCase):
    """
    Integration tests for Category views.
    """

    @classmethod
    def setUpTestData(cls):
        cls.category = Category.objects.create(
            CName="Test Category",
            Symbol="T",
            HasSubCat="1",
            CompId="COMP001",
            FinYearId="FY2023",
            SessionId="testuser",
        )

    def setUp(self):
        self.client = Client()
        # Mock session data
        session = self.client.session
        session["compid"] = "COMP001"
        session["finyear"] = "FY2023"
        session["username"] = "testuser"
        session.save()

    def test_category_list_view(self):
        """Tests the category list view renders correctly."""
        response = self.client.get(reverse("category_list"))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Work Order Categories")
        self.assertContains(response, "Test Category")

    def test_category_table_partial_view(self):
        """Tests the category table partial view."""
        response = self.client.get(reverse("category_table"))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Test Category")
        self.assertContains(response, "T")

    def test_category_create_view_get(self):
        """Tests GET request to category create view."""
        response = self.client.get(reverse("category_add"))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Add Work Order Category")

    def test_category_create_view_post_valid(self):
        """Tests POST request to category create view with valid data."""
        form_data = {
            "CName": "New Category",
            "Symbol": "N",
            "has_sub_cat_checkbox": True,
        }
        response = self.client.post(reverse("category_add"), data=form_data)
        # Should redirect or return 204 for HTMX
        self.assertIn(response.status_code, [200, 204, 302])

        # Verify category was created
        new_category = Category.objects.filter(CName="New Category").first()
        self.assertIsNotNone(new_category)
        self.assertEqual(new_category.Symbol, "N")
        self.assertEqual(new_category.HasSubCat, "1")

    def test_category_update_view_get(self):
        """Tests GET request to category update view."""
        response = self.client.get(
            reverse("category_edit", kwargs={"pk": self.category.pk})
        )
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Edit Work Order Category")
        self.assertContains(response, "Test Category")

    def test_category_delete_view_get(self):
        """Tests GET request to category delete view."""
        response = self.client.get(
            reverse("category_delete", kwargs={"pk": self.category.pk})
        )
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Confirm Deletion")
        self.assertContains(response, "Test Category")

    def test_category_delete_view_post(self):
        """Tests POST request to category delete view."""
        response = self.client.post(
            reverse("category_delete", kwargs={"pk": self.category.pk})
        )
        # Should redirect or return 204 for HTMX
        self.assertIn(response.status_code, [200, 204, 302])

        # Verify category was deleted
        self.assertFalse(Category.objects.filter(pk=self.category.pk).exists())


class SubCategoryViewTest(TestCase):
    """
    Integration tests for SubCategory views.
    """

    @classmethod
    def setUpTestData(cls):
        cls.category = Category.objects.create(
            CName="Parent Category",
            Symbol="P",
            HasSubCat="1",
            CompId="COMP001",
            FinYearId="FY2023",
            SessionId="testuser",
        )
        cls.subcategory = SubCategory.objects.create(
            CId=cls.category.CId,
            SCName="Test SubCategory",
            SCSymbol="S",
            CompId="COMP001",
            FinYearId="FY2023",
            SessionId="testuser",
        )

    def setUp(self):
        self.client = Client()
        # Mock session data
        session = self.client.session
        session["compid"] = "COMP001"
        session["finyear"] = "FY2023"
        session["username"] = "testuser"
        session.save()

    def test_subcategory_list_view(self):
        """Tests the subcategory list view renders correctly."""
        response = self.client.get(reverse("subcategory_list"))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Work Order Sub-Categories")
        self.assertContains(response, "Test SubCategory")

    def test_subcategory_table_partial_view(self):
        """Tests the subcategory table partial view."""
        response = self.client.get(reverse("subcategory_table"))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Test SubCategory")
        self.assertContains(response, "S")

    def test_subcategory_create_view_get(self):
        """Tests GET request to subcategory create view."""
        response = self.client.get(reverse("subcategory_add"))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Add Work Order Sub-Category")

    def test_subcategory_create_view_post_valid(self):
        """Tests POST request to subcategory create view with valid data."""
        form_data = {
            "CId": self.category.CId,
            "SCName": "New SubCategory",
            "SCSymbol": "N",
        }
        response = self.client.post(reverse("subcategory_add"), data=form_data)
        # Should redirect or return 204 for HTMX
        self.assertIn(response.status_code, [200, 204, 302])

        # Verify subcategory was created
        new_subcategory = SubCategory.objects.filter(SCName="New SubCategory").first()
        self.assertIsNotNone(new_subcategory)
        self.assertEqual(new_subcategory.SCSymbol, "N")
        self.assertEqual(new_subcategory.CId, self.category.CId)

    def test_subcategory_update_view_get(self):
        """Tests GET request to subcategory update view."""
        response = self.client.get(
            reverse("subcategory_edit", kwargs={"pk": self.subcategory.pk})
        )
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Edit Work Order Sub-Category")
        self.assertContains(response, "Test SubCategory")

    def test_subcategory_delete_view_get(self):
        """Tests GET request to subcategory delete view."""
        response = self.client.get(
            reverse("subcategory_delete", kwargs={"pk": self.subcategory.pk})
        )
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Confirm Deletion")
        self.assertContains(response, "Test SubCategory")

    def test_subcategory_delete_view_post(self):
        """Tests POST request to subcategory delete view."""
        response = self.client.post(
            reverse("subcategory_delete", kwargs={"pk": self.subcategory.pk})
        )
        # Should redirect or return 204 for HTMX
        self.assertIn(response.status_code, [200, 204, 302])

        # Verify subcategory was deleted
        self.assertFalse(SubCategory.objects.filter(pk=self.subcategory.pk).exists())
