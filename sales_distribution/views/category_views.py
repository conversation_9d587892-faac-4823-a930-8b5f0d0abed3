from django.views.generic import ListView, <PERSON><PERSON>View, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, Http404
from django.shortcuts import render
from django.template.loader import render_to_string
from ..models import Category, SubCategory
from ..forms import CategoryForm, SubCategoryForm


# Helper function to get session context
def get_session_context(request):
    """
    Retrieves company, financial year, and session user from the request session.
    Provides default values for testing/initial setup that match actual database data.
    """
    comp_id = request.session.get("compid", 1)  # Default to 1 to match existing data
    fin_year_id = request.session.get("finyear", 9)  # Default to 9 to match existing data
    session_id = request.session.get("username", "admin")
    return comp_id, fin_year_id, session_id


# Category Views
class CategoryListView(ListView):
    """Displays the main dashboard page for Categories."""

    model = Category
    template_name = "sales_distribution/category/category_list.html"
    context_object_name = "categories"

    def get_queryset(self):
        """Filters categories based on session context."""
        comp_id, fin_year_id, _ = get_session_context(self.request)
        return Category.get_filtered_categories(comp_id, fin_year_id)


class CategoryTablePartialView(ListView):
    """
    Renders only the category table, intended for HTMX requests to refresh
    the list dynamically without a full page reload.
    """

    model = Category
    template_name = "sales_distribution/category/_category_table.html"
    context_object_name = "categories"

    def get_queryset(self):
        """Filters categories based on session context."""
        comp_id, fin_year_id, _ = get_session_context(self.request)
        return Category.get_filtered_categories(comp_id, fin_year_id)


class CategoryCreateView(CreateView):
    """Handles creation of new categories via a modal form."""

    model = Category
    form_class = CategoryForm
    template_name = "sales_distribution/category/_category_form.html"
    success_url = reverse_lazy("category_list")

    def form_valid(self, form):
        """Sets audit/context fields before saving the new category."""
        comp_id, fin_year_id, session_id = get_session_context(self.request)

        # Assign audit/context fields from the session
        form.instance.CompId = comp_id
        form.instance.FinYearId = fin_year_id
        form.instance.SessionId = session_id

        response = super().form_valid(form)
        messages.success(self.request, "Category added successfully.")

        if self.request.headers.get("HX-Request"):
            return HttpResponse(
                status=204, headers={"HX-Trigger": "refreshCategoryList"}
            )
        return response

    def form_invalid(self, form):
        """Re-renders the form with validation errors for HTMX requests."""
        if self.request.headers.get("HX-Request"):
            return render(self.request, self.template_name, {"form": form})
        return super().form_invalid(form)


class CategoryUpdateView(UpdateView):
    """Handles updating existing categories via a modal form."""

    model = Category
    form_class = CategoryForm
    template_name = "sales_distribution/category/_category_form.html"
    success_url = reverse_lazy("category_list")

    def get_object(self, queryset=None):
        """Ensure the object retrieved belongs to the current session context."""
        obj = super().get_object(queryset)
        comp_id, fin_year_id, _ = get_session_context(self.request)
        if obj.CompId != comp_id or obj.FinYearId > fin_year_id:
            raise Http404("Category not found or not accessible.")
        return obj

    def form_valid(self, form):
        """Saves the updated category."""
        response = super().form_valid(form)
        messages.success(self.request, "Category updated successfully.")

        if self.request.headers.get("HX-Request"):
            return HttpResponse(
                status=204, headers={"HX-Trigger": "refreshCategoryList"}
            )
        return response

    def form_invalid(self, form):
        """Re-renders the form with validation errors for HTMX requests."""
        if self.request.headers.get("HX-Request"):
            return render(self.request, self.template_name, {"form": form})
        return super().form_invalid(form)


class CategoryDeleteView(DeleteView):
    """Handles deletion of categories via a confirmation modal."""

    model = Category
    template_name = "sales_distribution/category/_category_confirm_delete.html"
    success_url = reverse_lazy("category_list")

    def get_object(self, queryset=None):
        """Ensure the object retrieved belongs to the current session context."""
        obj = super().get_object(queryset)
        comp_id, fin_year_id, _ = get_session_context(self.request)
        if obj.CompId != comp_id or obj.FinYearId > fin_year_id:
            raise Http404("Category not found or not accessible.")
        return obj

    def delete(self, request, *args, **kwargs):
        """Deletes the category."""
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, "Category deleted successfully.")
        if request.headers.get("HX-Request"):
            return HttpResponse(
                status=204, headers={"HX-Trigger": "refreshCategoryList"}
            )
        return response


# SubCategory Views
class SubCategoryListView(ListView):
    """Displays the main dashboard page for SubCategories."""

    model = SubCategory
    template_name = "sales_distribution/category/subcategory_list.html"
    context_object_name = "subcategories"

    def get_queryset(self):
        """Filters subcategories based on session context."""
        comp_id, fin_year_id, _ = get_session_context(self.request)
        category_id = self.request.GET.get("category_id")
        return SubCategory.get_filtered_subcategories(comp_id, fin_year_id, category_id)

    def get_context_data(self, **kwargs):
        """Add categories for the dropdown filter."""
        context = super().get_context_data(**kwargs)
        comp_id, fin_year_id, _ = get_session_context(self.request)
        context["categories"] = Category.get_filtered_categories(comp_id, fin_year_id)
        context["selected_category_id"] = self.request.GET.get("category_id")
        return context


class SubCategoryTablePartialView(ListView):
    """
    Renders only the subcategory table, intended for HTMX requests to refresh
    the list dynamically without a full page reload.
    """

    model = SubCategory
    template_name = "sales_distribution/category/_subcategory_table.html"
    context_object_name = "subcategories"

    def get_queryset(self):
        """Filters subcategories based on session context."""
        comp_id, fin_year_id, _ = get_session_context(self.request)
        category_id = self.request.GET.get("category_id")
        return SubCategory.get_filtered_subcategories(comp_id, fin_year_id, category_id)


class SubCategoryCreateView(CreateView):
    """Handles creation of new subcategories via a modal form."""

    model = SubCategory
    form_class = SubCategoryForm
    template_name = "sales_distribution/category/_subcategory_form.html"
    success_url = reverse_lazy("subcategory_list")

    def get_form_kwargs(self):
        """Pass categories to the form."""
        kwargs = super().get_form_kwargs()
        comp_id, fin_year_id, _ = get_session_context(self.request)
        kwargs["categories"] = Category.get_filtered_categories(comp_id, fin_year_id)
        return kwargs

    def get_context_data(self, **kwargs):
        """Add categories for the dropdown."""
        context = super().get_context_data(**kwargs)
        comp_id, fin_year_id, _ = get_session_context(self.request)
        context["categories"] = Category.get_filtered_categories(comp_id, fin_year_id)
        return context

    def form_valid(self, form):
        """Sets audit/context fields before saving the new subcategory."""
        comp_id, fin_year_id, session_id = get_session_context(self.request)

        # Assign audit/context fields from the session
        form.instance.CompId = comp_id
        form.instance.FinYearId = fin_year_id
        form.instance.SessionId = session_id

        response = super().form_valid(form)
        messages.success(self.request, "Sub-Category added successfully.")

        if self.request.headers.get("HX-Request"):
            return HttpResponse(
                status=204, headers={"HX-Trigger": "refreshSubCategoryList"}
            )
        return response

    def form_invalid(self, form):
        """Re-renders the form with validation errors for HTMX requests."""
        if self.request.headers.get("HX-Request"):
            context = self.get_context_data(form=form)
            return render(self.request, self.template_name, context)
        return super().form_invalid(form)


class SubCategoryUpdateView(UpdateView):
    """Handles updating existing subcategories via a modal form."""

    model = SubCategory
    form_class = SubCategoryForm
    template_name = "sales_distribution/category/_subcategory_form.html"
    success_url = reverse_lazy("subcategory_list")

    def get_form_kwargs(self):
        """Pass categories to the form."""
        kwargs = super().get_form_kwargs()
        comp_id, fin_year_id, _ = get_session_context(self.request)
        kwargs["categories"] = Category.get_filtered_categories(comp_id, fin_year_id)
        return kwargs

    def get_context_data(self, **kwargs):
        """Add categories for the dropdown."""
        context = super().get_context_data(**kwargs)
        comp_id, fin_year_id, _ = get_session_context(self.request)
        context["categories"] = Category.get_filtered_categories(comp_id, fin_year_id)
        return context

    def get_object(self, queryset=None):
        """Ensure the object retrieved belongs to the current session context."""
        obj = super().get_object(queryset)
        comp_id, fin_year_id, _ = get_session_context(self.request)
        if obj.CompId != comp_id or obj.FinYearId > fin_year_id:
            raise Http404("Sub-Category not found or not accessible.")
        return obj

    def form_valid(self, form):
        """Saves the updated subcategory."""
        response = super().form_valid(form)
        messages.success(self.request, "Sub-Category updated successfully.")

        if self.request.headers.get("HX-Request"):
            return HttpResponse(
                status=204, headers={"HX-Trigger": "refreshSubCategoryList"}
            )
        return response

    def form_invalid(self, form):
        """Re-renders the form with validation errors for HTMX requests."""
        if self.request.headers.get("HX-Request"):
            context = self.get_context_data(form=form)
            return render(self.request, self.template_name, context)
        return super().form_invalid(form)


class SubCategoryDeleteView(DeleteView):
    """Handles deletion of subcategories via a confirmation modal."""

    model = SubCategory
    template_name = "sales_distribution/category/_subcategory_confirm_delete.html"
    success_url = reverse_lazy("subcategory_list")

    def get_object(self, queryset=None):
        """Ensure the object retrieved belongs to the current session context."""
        obj = super().get_object(queryset)
        comp_id, fin_year_id, _ = get_session_context(self.request)
        if obj.CompId != comp_id or obj.FinYearId > fin_year_id:
            raise Http404("Sub-Category not found or not accessible.")
        return obj

    def delete(self, request, *args, **kwargs):
        """Deletes the subcategory."""
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, "Sub-Category deleted successfully.")
        if request.headers.get("HX-Request"):
            return HttpResponse(
                status=204, headers={"HX-Trigger": "refreshSubCategoryList"}
            )
        return response
